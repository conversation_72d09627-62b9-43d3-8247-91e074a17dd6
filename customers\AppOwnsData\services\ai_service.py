# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import base64
import json
import os
import re
from typing import Any, Dict, List

from dotenv import load_dotenv
from google import genai
from google.genai import types

# Load environment variables
load_dotenv()


class AIService:
    """Service for handling AI-powered data analysis and summary generation"""

    def __init__(self):
        """Initialize the AI service with Google Generative AI"""
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("GOOGLE_API_KEY not found in environment variables")

        self.client = genai.Client(api_key=api_key)
        self.model = "gemini-2.0-flash"

    def clean_and_format_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Clean and format the raw Power BI data for AI processing

        Args:
            raw_data: Raw data from Power BI visuals

        Returns:
            Cleaned and formatted data (automatically excludes slicers, filters, and invalid data)
        """
        # cleaned_data = {}
        SYSTEM_PROMPT = """
You are a powerful data-cleaning assistant specialized in processing JSON data retrieved from Power BI Embedded reports.

Your objective is to clean the raw JSON data by removing any type of slicer, filter, or interactive constraint, while preserving all core data.

Instructions:
1. Parse the JSON input.
2. Remove all objects or fields related to filters, slicers, or user-defined constraints. These may include, but are not limited to:
   - Keys or objects named: "filters", "slicers", "filterPane", "selectionState", "appliedFilters", etc.
3. You may also receive an optional list of custom field names or paths that represent filters (e.g., `["regionSelector", "time_filter"]`). These should be treated as filters and removed if present.
4. Retain the rest of the data exactly as is. Do not modify values, field names, or structure unless it is directly related to the removal of filters or slicers.
5. Return the cleaned JSON, properly formatted and without any filter-related data.

Input Parameters:
- `json_input`: the raw JSON string from the Power BI embedded report.
- `custom_filter_keys` (optional): list of key names or paths that should be treated as filters/slicers, in case they use custom or nonstandard names.

Output:
- Cleaned JSON with all slicer/filter data removed.
"""

        contents = [
            types.Part.from_text(
                text=json.dumps(
                    f"json_input:\n{raw_data}\n\ncustom_filter_keys:\n[Slicers, Filters]"
                )
            )
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="application/json",
            system_instruction=[
                types.Part.from_text(text=SYSTEM_PROMPT),
            ],
        )

        response = self.client.models.generate_content(
            model=self.model,
            contents=contents,
            config=generate_content_config,
        )

        return json.loads(response.text)

    def generate_comprehensive_summary(self, cleaned_data: str) -> str:
        """
        Generate a comprehensive summary of the cleaned data

        Args:
            cleaned_data: Cleaned and formatted data from Power BI visuals

        Returns:
            Comprehensive summary of the data
        """
        SYSTEM_PROMPT = """
You are a highly intelligent data analyst. Your task is to analyze structured Power BI visual data exported in JSON format.

Each visual includes:
- `title`: name of the visual
- `data`: a string in CSV format
- `chartType` (optional): the type of visualization (e.g., bar chart, pie chart, line chart)
- `exportedAt`: timestamp of export (can be ignored)

Instructions:
1. For each visual in the "data" section of the JSON:
   - Parse the CSV-formatted string into a structured table.
   - If `chartType` is provided, use it to better understand what kind of insight is likely (e.g., pie chart shows proportions, bar chart shows comparisons).
   - Summarize the key insights from the data: trends, distributions, top/bottom items, anomalies, etc.
   - Provide a 1–2 sentence summary of each visual's meaning, tailored to its chart type and data content.

2. After reviewing all visuals:
   - Produce a comprehensive overall summary.
   - Highlight cross-visual patterns, dominant metrics, critical insights, or unusual data points.

Notes:
- Use numerical reasoning and comparison.
- If a visual's title is missing or unclear, rely on column headers and data values for interpretation.
- Output should be clear, business-friendly, and insight-driven.

Output format:
- **Visual Title**: [Insight summary]
- ...
- **Overall Summary**: [Combined insight]
"""

        contents = [
            types.Part.from_text(text=json.dumps(f"json_input:\n{cleaned_data}"))
        ]
        generate_content_config = types.GenerateContentConfig(
            response_mime_type="text/plain",
            system_instruction=[
                types.Part.from_text(text=SYSTEM_PROMPT),
            ],
        )

        summary_response = self.client.models.generate_content(
            model=self.model,
            contents=contents,
            config=generate_content_config,
        )

        return summary_response.text
