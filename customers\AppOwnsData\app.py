# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

import json
import os
from datetime import datetime
from io import BytesIO

from flask import (
    Flask,
    jsonify,
    render_template,
    request,
    send_file,
    send_from_directory,
)
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import inch
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer
from services.ai_service import AIService
from services.pbiembedservice import PbiEmbedService
from utils import Utils

# Initialize the Flask app
app = Flask(__name__)

# Load configuration
app.config.from_object("config.BaseConfig")


def create_pdf_from_summary(summary_text):
    """Create a PDF from the AI summary text"""
    buffer = BytesIO()

    # Create the PDF document
    doc = SimpleDocTemplate(
        buffer,
        pagesize=A4,
        rightMargin=inch,
        leftMargin=inch,
        topMargin=inch,
        bottomMargin=inch,
    )

    # Get styles
    styles = getSampleStyleSheet()

    # Create custom styles
    title_style = ParagraphStyle(
        "CustomTitle",
        parent=styles["Heading1"],
        fontSize=18,
        spaceAfter=30,
        alignment=TA_CENTER,
        textColor="#2E86AB",
    )

    heading_style = ParagraphStyle(
        "CustomHeading",
        parent=styles["Heading2"],
        fontSize=14,
        spaceAfter=12,
        spaceBefore=20,
        textColor="#2E86AB",
    )

    body_style = ParagraphStyle(
        "CustomBody",
        parent=styles["Normal"],
        fontSize=11,
        spaceAfter=12,
        alignment=TA_LEFT,
        leftIndent=0,
    )

    # Build the PDF content
    story = []

    # Add title
    story.append(Paragraph("AI-Powered Report Analysis", title_style))
    story.append(Spacer(1, 20))

    # Add timestamp
    timestamp = datetime.now().strftime("%B %d, %Y at %I:%M %p")
    story.append(Paragraph(f"Generated on: {timestamp}", body_style))
    story.append(Spacer(1, 30))

    # Process the summary text
    lines = summary_text.split("\n")
    current_section = []

    for line in lines:
        line = line.strip()
        if not line:
            if current_section:
                # Add current section content
                content = "\n".join(current_section)
                story.append(Paragraph(content, body_style))
                story.append(Spacer(1, 12))
                current_section = []
            continue

        # Check if line is a heading (starts with ** or contains Visual Title pattern)
        if line.startswith("**") and line.endswith("**"):
            # Add previous section if exists
            if current_section:
                content = "\n".join(current_section)
                story.append(Paragraph(content, body_style))
                story.append(Spacer(1, 12))
                current_section = []

            # Add heading
            heading_text = line.strip("*").strip()
            story.append(Paragraph(heading_text, heading_style))
        elif ":" in line and (
            line.startswith("**") or "Visual" in line or "Summary" in line
        ):
            # Add previous section if exists
            if current_section:
                content = "\n".join(current_section)
                story.append(Paragraph(content, body_style))
                story.append(Spacer(1, 12))
                current_section = []

            # Add as heading
            story.append(Paragraph(line.replace("**", ""), heading_style))
        else:
            # Add to current section
            current_section.append(line)

    # Add any remaining content
    if current_section:
        content = "\n".join(current_section)
        story.append(Paragraph(content, body_style))

    # Build PDF
    doc.build(story)
    buffer.seek(0)
    return buffer


@app.route("/")
def index():
    """Returns a static HTML page"""

    return render_template("index.html")


@app.route("/getembedinfo", methods=["GET"])
def get_embed_info():
    """Returns report embed configuration"""

    config_result = Utils.check_config(app)
    if config_result is not None:
        return json.dumps({"errorMsg": config_result}), 500
    try:
        embed_info = PbiEmbedService().get_embed_params_for_single_report(
            app.config["WORKSPACE_ID"], app.config["REPORT_ID"]
        )
        return embed_info
    except Exception as ex:
        return json.dumps({"errorMsg": str(ex)}), 500


@app.route("/generate-summary", methods=["POST"])
def generate_summary():
    """Generate AI-powered summary from Power BI data and return as PDF"""

    try:
        # Get the data from the request
        data = request.get_json()
        if not data:
            return jsonify({"errorMsg": "No data provided"}), 400

        # Initialize AI service
        ai_service = AIService()

        # Clean and format the data
        cleaned_data = ai_service.clean_and_format_data(data)

        if not cleaned_data:
            return jsonify({"errorMsg": "No valid data found for analysis"}), 400

        # Generate comprehensive summary
        summary_result = ai_service.generate_comprehensive_summary(
            json.dumps(cleaned_data)
        )

        # Create PDF from summary
        pdf_buffer = create_pdf_from_summary(summary_result)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"PowerBI_AI_Summary_{timestamp}.pdf"

        # Return PDF file for download
        return send_file(
            pdf_buffer,
            as_attachment=True,
            download_name=filename,
            mimetype="application/pdf",
        )

    except ValueError as ve:
        return jsonify({"errorMsg": f"Configuration error: {str(ve)}"}), 500
    except Exception as ex:
        return jsonify({"errorMsg": f"Error generating AI summary: {str(ex)}"}), 500


@app.route("/favicon.ico", methods=["GET"])
def getfavicon():
    """Returns path of the favicon to be rendered"""

    return send_from_directory(
        os.path.join(app.root_path, "static"),
        "img/favicon.ico",
        mimetype="image/vnd.microsoft.icon",
    )


if __name__ == "__main__":
    app.run(debug=True)
