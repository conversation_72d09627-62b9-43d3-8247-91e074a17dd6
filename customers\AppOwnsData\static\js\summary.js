// Copyright (c) Microsoft Corporation.
// Licensed under the MIT license.

$(document).ready(function() {
    // Load and display the AI summary
    loadSummary();
    
    // Print functionality
    $("#print-summary").on("click", function() {
        window.print();
    });
});

function loadSummary() {
    try {
        // Get summary data from sessionStorage
        const summaryData = sessionStorage.getItem('aiSummary');
        
        if (!summaryData) {
            showError("No summary data found. Please generate a new summary from the main report.");
            return;
        }
        
        const summary = JSON.parse(summaryData);
        
        // Hide loading and show content
        $("#loading-container").hide();
        $("#summary-content").show();
        
        // Populate overall insights
        if (summary.overall_insights) {
            $("#overall-insights").html(formatText(summary.overall_insights));
        } else {
            $("#overall-insights").html("<p>No overall insights available.</p>");
        }
        
        // Populate individual visual summaries
        if (summary.visual_summaries) {
            populateVisualSummaries(summary.visual_summaries);
        }
        
        // Populate metadata
        if (summary.metadata) {
            populateMetadata(summary.metadata);
        }
        
    } catch (error) {
        console.error("Error loading summary:", error);
        showError("Error loading summary data. Please try generating a new summary.");
    }
}

function populateVisualSummaries(visualSummaries) {
    const container = $("#visual-summaries");
    container.empty();
    
    Object.keys(visualSummaries).forEach(function(visualKey) {
        const visualData = visualSummaries[visualKey];
        
        const cardHtml = `
            <div class="card visual-summary-card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-chart-bar"></i> ${escapeHtml(visualKey)}
                    </h3>
                </div>
                <div class="card-body">
                    <div class="summary-section">
                        <div class="section-title">
                            <i class="fas fa-star"></i> Executive Summary
                        </div>
                        <div class="executive-summary">
                            ${formatText(visualData.executive_summary || 'No executive summary available.')}
                        </div>
                    </div>
                    
                    <div class="summary-section">
                        <div class="section-title">
                            <i class="fas fa-microscope"></i> Detailed Analysis
                        </div>
                        <div class="detailed-analysis">
                            ${formatText(visualData.detailed_analysis || 'No detailed analysis available.')}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.append(cardHtml);
    });
}

function populateMetadata(metadata) {
    if (metadata.total_visuals_analyzed) {
        $("#total-visuals").text(metadata.total_visuals_analyzed);
    }
    
    if (metadata.analysis_timestamp) {
        const timestamp = new Date(metadata.analysis_timestamp);
        $("#analysis-timestamp").text(timestamp.toLocaleString());
    }
}

function formatText(text) {
    if (!text) return '';
    
    // Convert text to HTML with proper formatting
    let formatted = escapeHtml(text);
    
    // Convert line breaks to HTML
    formatted = formatted.replace(/\n\n/g, '</p><p>');
    formatted = formatted.replace(/\n/g, '<br>');
    
    // Wrap in paragraph tags if not already formatted
    if (!formatted.includes('<p>')) {
        formatted = '<p>' + formatted + '</p>';
    }
    
    // Format bullet points
    formatted = formatted.replace(/^[\s]*[-•*]\s+(.+)$/gm, '<li>$1</li>');
    
    // Wrap consecutive list items in ul tags
    formatted = formatted.replace(/(<li>.*<\/li>)/gs, function(match) {
        return '<ul>' + match + '</ul>';
    });
    
    // Format numbered lists
    formatted = formatted.replace(/^[\s]*\d+\.\s+(.+)$/gm, '<li>$1</li>');
    
    // Clean up any double ul tags
    formatted = formatted.replace(/<\/ul>\s*<ul>/g, '');
    
    // Bold text formatting for key phrases
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    formatted = formatted.replace(/__(.*?)__/g, '<strong>$1</strong>');
    
    return formatted;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showError(message) {
    $("#loading-container").hide();
    $("#summary-content").hide();
    $("#error-message").text(message);
    $("#error-container").show();
}

// Handle browser back/forward navigation
window.addEventListener('beforeunload', function() {
    // Clear the summary data when leaving the page
    sessionStorage.removeItem('aiSummary');
});

// Handle print-specific formatting
window.addEventListener('beforeprint', function() {
    // Add any print-specific formatting here if needed
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    // Remove print-specific formatting
    document.body.classList.remove('printing');
});
